# Security Service Environment Variables

# Server Configuration
PORT=5010
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-secret-key
JWT_ACCESS_EXPIRATION=1h
JWT_REFRESH_EXPIRATION=7d
JWT_VERIFICATION_EXPIRATION=24h
JWT_RESET_EXPIRATION=1h

# Service Secrets
CONFIG_SERVICE_SECRET=stage7AuthSecret
POSTOFFICE_SECRET=stage7AuthSecret
MISSIONCONTROL_SECRET=stage7AuthSecret
BRAIN_SECRET=stage7AuthSecret
LIBRARIAN_SECRET=stage7AuthSecret
ENGINEER_SECRET=stage7AuthSecret
TRAFFICMANAGER_SECRET=stage7AuthSecret
CAPABILITIESMANAGER_SECRET=stage7AuthSecret
AGENTSET_SECRET=stage7AuthSecret

# Database Configuration
LIBRARIAN_URL=librarian:5040

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
EMAIL_FROM=<EMAIL>

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:3000

# Account Security
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=30 # minutes
