.text-input {
  margin-bottom: 20px;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.text-input form {
  display: flex;
  flex-direction: column;
}

.text-input textarea {
  width: 100%;
  padding: 10px 15px;
  font-size: 16px;
  border: 2px solid #ddd;
  border-radius: 4px;
  outline: none;
  transition: border-color 0.3s ease;
  resize: vertical;
  min-height: 100px;
}

.text-input textarea:focus {
  border-color: #007bff;
}

.text-input button[type="submit"] {
  margin-top: 10px;
  padding: 10px 20px;
  font-size: 16px;
  color: #fff;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  align-self: flex-end;
}

.text-input button[type="submit"]:hover {
  background-color: #0056b3;
}

.text-input button[type="submit"]:active {
  background-color: #004085;
}

@media (max-width: 600px) {
  .text-input {
    width: 90%;
  }
}