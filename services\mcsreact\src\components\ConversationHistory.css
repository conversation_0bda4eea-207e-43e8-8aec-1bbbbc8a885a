.conversation-history {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.conversation-history h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 15px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.history-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
}

.history-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease;
}

.history-item:hover {
  background-color: #e9ecef;
}

.history-item:nth-child(odd) {
  background-color: #e3f2fd;
}

.history-item:nth-child(odd):hover {
  background-color: #bbdefb;
}

/* Scrollbar styles for WebKit browsers */
.history-list::-webkit-scrollbar {
  width: 8px;
}

.history-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.history-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@media (max-width: 768px) {
  .conversation-history {
    padding: 15px;
  }

  .history-list {
    max-height: 300px;
  }

  .history-item {
    padding: 10px;
  }
}