{"compilerOptions": {"target": "ES2020", "module": "commonjs", "moduleResolution": "node", "declaration": true, "outDir": "./dist", "strict": true, "paths": {"@cktmcs/shared/*": ["./src/*"]}, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "rootDir": "./src", "types": ["node", "jest"], "skipLibCheck": true, "preserveSymlinks": true, "resolveJsonModule": true, "sourceMap": true, "inlineSources": true, "allowJs": true, "importHelpers": true, "moduleDetection": "force"}, "include": ["src/**/*", "src/types/*.d.ts"], "exclude": ["node_modules", "dist"]}