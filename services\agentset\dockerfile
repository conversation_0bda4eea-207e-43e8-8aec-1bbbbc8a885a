# Use an official Node.js runtime as the base image
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./
COPY shared/package*.json ./shared/
COPY errorhandler/package*.json ./errorhandler/
COPY services/agentset/package*.json ./services/agentset/

# Install the application dependencies
RUN npm install

# Copy the application code to the working directory
COPY shared ./shared
COPY errorhandler ./errorhandler
COPY services/agentset ./services/agentset

# Build the TypeScript code
RUN cd shared && npm run build
RUN cd errorhandler && npm run build
RUN npm run build --workspace=services/agentset

# Set the working directory to the service
WORKDIR /usr/src/app/services/agentset

# Expose the port the app runs on
EXPOSE 9000

# Define the command to run the application
CMD ["node", "--expose-gc","dist/AgentSet.js"]