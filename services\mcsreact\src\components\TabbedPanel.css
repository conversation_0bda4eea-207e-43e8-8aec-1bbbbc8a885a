.tabbed-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tab-buttons {
  display: flex;
  background-color: #f0f0f0;
  border-bottom: 1px solid #ccc;
}

.tab-buttons button {
  padding: 10px 20px;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

.tab-buttons button.active {
  background-color: #fff;
  border-bottom: 2px solid #007bff;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.work-products table {
  width: 100%;
  border-collapse: collapse;
}

.work-products th, .work-products td {
  text-align: left;
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

.work-products th:first-child, .work-products td:first-child {
  width: 100px;
}

.work-products a {
  color: #007bff;
  text-decoration: none;
}

.work-products a:hover {
  text-decoration: underline;
}