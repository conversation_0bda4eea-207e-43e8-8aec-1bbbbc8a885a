# Use an official Node.js runtime as the base image
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./
COPY shared/package*.json ./shared/
COPY errorhandler/package*.json ./errorhandler/
COPY services/postoffice/package*.json ./services/postoffice/

# Install the application dependencies
RUN npm install
RUN cd shared && npm install
RUN cd errorhandler && npm install
RUN cd services/postoffice && npm install

# Copy the application code to the working directory
COPY shared ./shared
COPY errorhandler ./errorhandler
COPY services/postoffice ./services/postoffice

# Build the TypeScript code - ensure shared is built first
RUN cd shared && npm run build
RUN cd errorhandler && npm run build
RUN cd services/postoffice && npm run build

# Set the working directory to the service
WORKDIR /usr/src/app/services/postoffice

# Expose the port the app runs on
EXPOSE 5020

# Define the command to run the application
CMD ["node", "dist/PostOffice.js"]
