{"compilerOptions": {"target": "ES2020", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "resolveJsonModule": true, "sourceMap": true, "importHelpers": true, "baseUrl": ".", "paths": {"@cktmcs/shared": ["../../shared/dist"], "@cktmcs/shared/*": ["../../shared/dist/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts"]}