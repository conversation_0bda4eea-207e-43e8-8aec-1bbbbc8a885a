{"compilerOptions": {"target": "ES2020", "module": "commonjs", "declaration": true, "declarationMap": true, "outDir": "./dist", "moduleResolution": "node", "strict": true, "paths": {"@cktmcs/errorhandler/*": ["./src/*"]}, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "rootDir": "./src", "types": ["node", "jest"], "skipLibCheck": true}, "include": ["src"], "exclude": ["node_modules", "dist"]}