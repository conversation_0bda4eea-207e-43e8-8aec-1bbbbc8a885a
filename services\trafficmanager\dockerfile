# Use an official Node.js runtime as the base image
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./
COPY shared/package*.json ./shared/
COPY errorhandler/package*.json ./errorhandler/
COPY services/trafficmanager/package*.json ./services/trafficmanager/

# Install the application dependencies
RUN npm install

# Copy the application code to the working directory
COPY shared ./shared
COPY errorhandler ./errorhandler
COPY services/trafficmanager ./services/trafficmanager

# Build the TypeScript code
RUN npm run build --workspace=shared
RUN npm run build --workspace=errorhandler
RUN npm run build --workspace=services/trafficmanager

# Set the working directory to the service
WORKDIR /usr/src/app/services/trafficmanager

# Expose the port the app runs on
EXPOSE 5080

# Define the command to run the application
CMD ["node", "dist/TrafficManager.js"]