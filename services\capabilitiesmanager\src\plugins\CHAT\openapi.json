{"openapi": "3.0.0", "info": {"title": "CHAT Plugin API", "version": "1.0.0", "description": "API for managing interactive chat sessions."}, "paths": {"/start_chat": {"post": {"summary": "Start a new chat session", "operationId": "start_chat", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"initial_message": {"type": "string", "description": "An introductory message to start the chat."}}, "required": ["initial_message"]}}}}, "responses": {"200": {"description": "Chat session started successfully.", "content": {"application/json": {"schema": {"type": "object", "properties": {"session_id": {"type": "string", "description": "A unique identifier for the chat session."}, "response": {"type": "string", "description": "The user's first response."}}}}}}}}}, "/send_message": {"post": {"summary": "Send a message to the user", "operationId": "send_message", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"session_id": {"type": "string", "description": "The ID of the current chat session."}, "message": {"type": "string", "description": "The message to send to the user."}}, "required": ["session_id", "message"]}}}}, "responses": {"200": {"description": "Message sent and response received.", "content": {"application/json": {"schema": {"type": "object", "properties": {"response": {"type": "string", "description": "The user's response to the message."}}}}}}}}}, "/end_chat": {"post": {"summary": "End the chat session", "operationId": "end_chat", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"session_id": {"type": "string", "description": "The ID of the chat session to end."}}, "required": ["session_id"]}}}}, "responses": {"200": {"description": "Chat session ended successfully.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "A confirmation message."}}}}}}}}}}}