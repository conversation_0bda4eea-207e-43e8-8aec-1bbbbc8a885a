.auth-container {
    max-width: 300px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-container form {
    display: flex;
    flex-direction: column;
}

.auth-container h2 {
    text-align: center;
    margin-bottom: 20px;
}

.auth-container input {
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.auth-container button[type="submit"] {
    padding: 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.auth-container button[type="submit"]:hover {
    background-color: #0056b3;
}

.auth-container p {
    text-align: center;
    margin-top: 15px;
}

.toggle-auth {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
}

.toggle-auth:hover {
    color: #0056b3;
}