# ACCOMPLISH Plugin and Plan Parsing Enhancements

This document details proposed enhancements to the `ACCOMPLISH` plugin's prompting strategy, its internal error handling mechanisms, and the logic within `Step.createFromPlan` for more robust plan parsing and execution. These changes aim to improve the reliability, debuggability, and overall quality of AI-generated plans.

## 1. Refining `ACCOMPLISH` Prompt

The quality of plans generated by `ACCOMPLISH` heavily depends on the clarity and specificity of the prompts used to interact with the underlying LLM. The following refinements aim to guide the LLM towards producing more robust, executable, and maintainable plans.

### 1.1. Clarity and Uniqueness of Output Names

*   **Problem:** LLMs might generate generic output names (e.g., "result", "data") or reuse names within a plan, leading to ambiguity.
*   **Refinement:**
    *   Instruct the LLM to assign a **descriptive and unique `name`** to *each distinct output* a plugin step produces.
    *   If a plugin is expected to produce multiple pieces of data for different downstream consumers (e.g., a raw dataset and a summary report), the LLM must define a separate, clearly named output for each.
    *   **Prompt Element:** "For each step in the plan, define its `outputs`. Each output must have a `name` that is unique within that step and clearly describes the data it represents (e.g., `cleaned_text_corpus`, `user_sentiment_score`, `generated_image_path`). Avoid generic names like 'output' or 'result' if multiple distinct outputs exist or if specificity is needed for downstream steps."

### 1.2. Matching Plan `outputKey` to Plugin Output `name`

*   **Problem:** The `outputKey` (or similar field in a consuming step's input definition) that references a producer step's output might not accurately match the `name` declared by the producer plugin.
*   **Refinement:**
    *   Emphasize that the `outputKey` used in an input mapping (e.g., `inputs: {"target_input_name": "steps.producer_step_id.actual_output_name"}`) must **exactly match** one of the `name`s in the `outputs` array of the specified producer step's manifest.
    *   **Prompt Element:** "When a step consumes output from a previous step, ensure the `outputKey` in its input mapping (e.g., `steps.SOURCE_STEP_ID.OUTPUT_NAME`) precisely matches the `name` of an output declared by `SOURCE_STEP_ID`. Do not invent or assume output names."

### 1.3. Inclusion of Error Handling and Fallback Steps

*   **Problem:** LLM-generated plans often represent only the "happy path" and lack instructions for handling failures.
*   **Refinement:**
    *   Encourage the LLM to include conditional logic or fallback steps in the plan.
    *   **Prompt Element:** "Consider potential failure points in the plan. For critical steps, define fallback actions or alternative steps if the primary step fails. You can use a conditional structure, for example: `{'type': 'conditional', 'condition_step': 'step_X_id', 'on_success': ['step_Y_id'], 'on_failure': ['step_Z_id']}`. Alternatively, provide clear instructions for error reporting if a step is unrecoverable."

### 1.4. Guidance on Using Transformation Steps

*   **Problem:** Output from one plugin might not be directly compatible with the input of another.
*   **Refinement:**
    *   Guide the LLM to recognize potential data incompatibilities (based on type descriptions if available, or common sense) and suggest inserting small, dedicated transformation plugins (e.g., "ConvertCSVtoJSON", "ResizeImage").
    *   **Prompt Element:** "If the output format of one step (e.g., `StepA` outputs JSON) is not directly compatible with the input requirements of a subsequent step (e.g., `StepB` expects XML), insert an intermediate transformation step. Clearly define the input for this transformation step (from `StepA`) and its output (for `StepB`). If a suitable transformation plugin doesn't exist, note it as a 'new_plugin_required' with clear specifications."

### 1.5. Suggestions for Iterative Planning for Complex Goals

*   **Problem:** Very complex goals can lead to overly long or convoluted plans that are difficult for the LLM to generate correctly in one shot.
*   **Refinement:**
    *   Instruct the LLM to break down complex goals into smaller, manageable sub-goals. It can generate a high-level plan first, then elaborate on each high-level step, possibly requesting user feedback or confirmation at intermediate stages.
    *   **Prompt Element:** "For highly complex goals, consider a multi-stage planning approach. First, outline a high-level plan with major phases. Then, elaborate on each phase, potentially generating detailed sub-plans. If uncertain about a particular approach or if user input is beneficial, include a 'USER_FEEDBACK_REQUIRED' marker with specific questions for the user before proceeding with further planning."

### 1.6. Handling of Resource Cleanup

*   **Problem:** Plugins might create resources (e.g., temporary files, database connections, external service sessions) that need explicit cleanup.
*   **Refinement:**
    *   Prompt the LLM to consider the lifecycle of resources used or created by plugins. If a plugin creates a resource that requires explicit release, the plan should include a subsequent step to perform this cleanup.
    *   **Prompt Element:** "If any step in the plan involves acquiring or creating resources that need explicit cleanup (e.g., opening files, starting temporary services, allocating external resources), ensure the plan includes a corresponding step to release or clean up these resources once they are no longer needed or at the end of the plan. This might involve calling a specific 'cleanup' verb of a plugin or a dedicated cleanup plugin."

## 2. Improving Error Handling in `ACCOMPLISH`

Robust error handling within the `ACCOMPLISH` plugin itself is crucial for diagnosing issues, especially when interacting with LLMs.

### 2.1. Use More Specific Error Types/Codes

*   **Problem:** Generic error messages make it hard to pinpoint the cause of failures.
*   **Proposal:** Define and use specific error types or codes for different failure scenarios within `ACCOMPLISH`.
*   **Examples:**
    *   `ACCOMPLISH_ERROR_LLM_NO_PLAN_GENERATED`: LLM failed to return any plan structure.
    *   `ACCOMPLISH_ERROR_LLM_PLAN_MALFORMED`: LLM returned a plan, but it's structurally invalid (e.g., missing required fields, incorrect JSON).
    *   `ACCOMPLISH_ERROR_LLM_OUTPUT_UNCLEAR`: LLM output is ambiguous or doesn't meet criteria.
    *   `ACCOMPLISH_ERROR_BRAIN_QUERY_FAILED`: Internal call to `queryBrain` (or other LLM service) failed.
    *   `ACCOMPLISH_ERROR_MAX_RETRIES_REACHED`: Retries for an operation (e.g., `queryBrain`) were exhausted.
*   **Benefit:** Allows for more targeted error handling, better monitoring, and clearer diagnostics.

### 2.2. Log LLM Conversation/Trace IDs

*   **Problem:** Debugging LLM interactions without a trace of the conversation is very difficult.
*   **Proposal:** Ensure that `ACCOMPLISH` logs any available conversation IDs, trace IDs, or request IDs provided by the LLM service.
*   **Benefit:** These IDs are invaluable for looking up the exact interaction in the LLM provider's logs, understanding the context, and diagnosing why the LLM produced a certain output or error.

### 2.3. Implement Retry Logic for `queryBrain`

*   **Problem:** Calls to external LLM services (`queryBrain`) can fail due to transient network issues or temporary service overload.
*   **Proposal:** Implement a configurable retry mechanism for `queryBrain` calls.
    *   **Strategy:** Use exponential backoff with jitter to avoid overwhelming the service.
    *   **Configuration:** Make retry attempts and backoff parameters configurable.
*   **Benefit:** Increases the resilience of `ACCOMPLISH` to temporary issues with the LLM service.

## 3. Strengthening `Step.createFromPlan` Logic

`Step.createFromPlan` (or similar functionality responsible for parsing the LLM-generated plan and instantiating executable `Step` objects) needs to be robust to variations and potential ambiguities in the plan.

### 3.1. Robust Mapping of `sourceStepNo` to `sourceStepId`

*   **Problem:** Plans might refer to previous steps using sequential numbers (`sourceStepNo`), which can be ambiguous if steps are reordered or if the plan uses a different numbering scheme than a simple array index.
*   **Proposal:**
    1.  **Preferred: Unique `stepId` in Plan:** The `ACCOMPLISH` prompt should strongly encourage the LLM to assign a unique `stepId` (e.g., `step_extract_keywords`, `step_summarize_text`) to each step in the generated plan. Subsequent steps should then reference these explicit `stepId`s.
        *   **Example Plan Element:** `{"stepId": "extract_user_info", "plugin": "UserInfoPlugin", ...}`
        *   **Reference:** `inputs: {"userData": "steps.extract_user_info.userDetails"}`
    2.  **If Sequential Numbers are Used:** If `sourceStepNo` is unavoidable and refers to a 1-based or 0-based index:
        *   `Step.createFromPlan` must maintain a reliable map of `(number_in_plan_definition) -> (actual_step_object_or_id)` as it parses the plan.
        *   Care must be taken with how these numbers are assigned by the LLM and interpreted during parsing. Consistent 0-based or 1-based indexing must be enforced by the prompt and parsing logic.
*   **Benefit:** Reduces ambiguity and makes plan references more robust.

### 3.2. Heuristic Validation for `outputName` Consistency

*   **Problem:** A plan might specify an `outputName` from a source step that the source plugin doesn't actually produce.
*   **Proposal:** During `Step.createFromPlan` (or a separate plan validation phase before execution):
    *   When an input references an output from a previous step (e.g., `inputs: {"target": "steps.source_step_id.source_output_name"}`):
        1.  Verify that `source_step_id` corresponds to an already parsed step in the current plan.
        2.  **Heuristic Check:** If possible (e.g., if plugin manifests are partially available or cached during planning/parsing), attempt to check if the plugin associated with `source_step_id` actually declares an output matching `source_output_name`. This is a "heuristic" because full manifest resolution might only occur at runtime.
        3.  Log a warning if the output name seems inconsistent or cannot be preliminarily verified.
*   **Benefit:** Catches potential plan errors earlier, even if full validation only happens at runtime.

### 3.3. Handling of Default/Implicit Outputs

*   **Problem:** Plugins might return a single, unnamed output, or plans might implicitly refer to "the" output of a step without naming it.
*   **Proposal:**
    1.  **Discourage via Prompt:** The `ACCOMPLISH` prompt refinements (Section 1.1) should heavily discourage the LLM from generating plans that rely on implicit or default outputs. Every output consumed should be explicitly named.
    2.  **Strict Parsing (Preferred):** `Step.createFromPlan` should ideally require all input-output mappings to be explicit (i.e., `steps.source_step_id.explicit_output_name`). If an explicit output name is missing in the reference, it should be treated as a plan parsing error.
    3.  **Last Resort Handling (If Necessary and with Warnings):** If strictness is not immediately achievable:
        *   If a plan references a step's output implicitly (e.g., `inputs: {"data": "steps.source_step_id"}`), `Step.createFromPlan` *could* be designed to assume a "default" output name (e.g., `output`, `result`).
        *   This practice is risky and should generate prominent warnings during plan parsing.
        *   The "default" output name would need to be a convention that plugins producing single, unnamed outputs adhere to.
*   **Benefit:** Enforcing explicit output naming makes plans clearer, less ambiguous, and easier to debug. Avoiding implicit outputs reduces the chances of runtime errors due to mismatched expectations.

## 4. Conclusion

These enhancements to `ACCOMPLISH`'s prompting, internal error management, and the plan parsing logic in `Step.createFromPlan` are designed to work in concert. Better prompts lead to better plans. Better error handling in `ACCOMPLISH` makes it more resilient and easier to debug. More robust plan parsing ensures that the generated plans are interpreted correctly and that potential issues are caught early. This holistic approach will contribute significantly to the overall reliability and effectiveness of the AI-driven planning and execution system.
