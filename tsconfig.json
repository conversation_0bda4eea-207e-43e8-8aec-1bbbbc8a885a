{
  "compilerOptions": {
    "target": "es2019", // Or a newer version like ES2020, ES2021
    "module": "commonjs",
    "lib": ["es2019", "dom"], // "dom" might be needed if any browser-like globals are used, otherwise remove
    "declaration": true,
    "outDir": "./dist", // Or your preferred output directory
    "rootDir": "./",    // Assuming source files are in root or subdirectories like 'src'
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true, // Good for Babel compatibility if used
    "experimentalDecorators": true, // If using decorators
    "emitDecoratorMetadata": true,   // If using decorators with metadata
    "baseUrl": ".", // Base directory for module resolution
    "paths": { // For module aliases
      "@cktmcs/shared": ["shared/src"], // Assuming shared module's main entry point is shared/src/index.ts or similar
      "@cktmcs/shared/*": ["shared/src/*"],
      "@cktmcs/errorhandler": ["errorhandler/src"],
      "@cktmcs/errorhandler/*": ["errorhandler/src/*"]
    },
    "typeRoots": ["./node_modules/@types", "./src/types"] // Include project-specific types if any
  },
  "include": [
    "services/**/*", // Include all ts files in services
    "shared/src/**/*",    // Include all ts files in shared/src
    "errorhandler/src/**/*" // Include errorhandler source
    // Add other paths like "src/**/*" if your project structure is different
  ],
  "exclude": [
    "node_modules",
    "dist", // Exclude output directory
    "**/*.test.ts", // Usually test files are not part of the main compilation output
    "**/*.spec.ts"  // Or if you use .spec.ts
  ]
}
