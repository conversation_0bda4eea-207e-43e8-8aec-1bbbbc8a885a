{"name": "@cktmcs/mission-control", "version": "1.0.0", "description": "MissionControl component for managing missions", "main": "dist/MissionControl.js", "scripts": {"start": "ts-node src/MissionControl.ts", "build": "tsc"}, "dependencies": {"@cktmcs/errorhandler": "file:../../errorhandler", "@cktmcs/shared": "file:../../shared", "axios": "^1.8.4", "body-parser": "^1.19.0", "cors": "^2.8.5", "express": "^4.21.1", "express-rate-limit": "^7.5.0", "uuid": "^10.0.0", "ws": "^8.18.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.12", "ts-node": "^10.9.1", "typescript": "^5.6.3"}}