{"name": "cktmcs", "private": true, "workspaces": ["shared", "errorhandler", "marketplace", "services/*"], "scripts": {"build": "npm run build --workspaces", "start": "npm run start --workspaces"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.7", "react-scripts": "^5.0.1", "ts-jest": "^29.4.0"}, "overrides": {"glob": "^10.4.5", "typescript": "^5.6.3", "inflight": "^2.0.0", "uuid": "^10.0.0", "@svgr/webpack": "^8.1.0", "svgo": "^2.8.0"}, "dependencies": {"axios": "^1.8.4", "isolated-vm": "^5.0.4", "jsonwebtoken": "^9.0.2", "ws": "^8.18.2"}}