# Use an official Node.js runtime as the base image
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./
COPY shared/package*.json ./shared/
COPY errorhandler/package*.json ./errorhandler/
COPY services/security/package*.json ./services/security/

# Install the application dependencies
RUN npm install

# Copy the application code to the working directory
COPY shared ./shared
COPY errorhandler ./errorhandler
COPY services/security ./services/security

# Build the shared and errorhandler packages
RUN cd shared && npm run build
RUN cd errorhandler && npm run build
RUN cd services/security && npm run build

# Create keys directory
RUN mkdir -p /usr/src/app/services/security/keys

# Set the working directory to the service
WORKDIR /usr/src/app/services/security

# Expose the port the app runs on
EXPOSE 5010

# Define the command to run the application
CMD ["node", "dist/SecurityManager.js"]
