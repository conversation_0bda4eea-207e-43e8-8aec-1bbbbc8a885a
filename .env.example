# Example environment variables for Stage7
# Copy this file to .env and fill in the values

# API Keys
GROQ_API_KEY=your_groq_api_key_here

# GitHub Integration
GITHUB_TOKEN=your_github_token_here
GITHUB_USERNAME=your_github_username_here
GIT_REPOSITORY_URL=https://github.com/your-username/your-repo.git
GIT_DEFAULT_BRANCH=main
GITHUB_EMAIL=<EMAIL>

# Repository Configuration
DEFAULT_PLUGIN_REPOSITORY=mongo  # Options: mongo, github, local
MONGO_COLLECTION=plugins

# Other Configuration
NODE_ENV=production
