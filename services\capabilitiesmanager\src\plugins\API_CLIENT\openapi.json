{"openapi": "3.0.0", "info": {"title": "API_CLIENT Plugin API", "version": "1.0.0", "description": "API for making requests to third-party RESTful APIs."}, "paths": {"/make_request": {"post": {"summary": "Make an HTTP request", "operationId": "make_request", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"method": {"type": "string", "description": "The HTTP method."}, "url": {"type": "string", "description": "The API endpoint URL."}, "headers": {"type": "object", "description": "A dictionary of HTTP headers."}, "body": {"type": "object", "description": "The request body."}, "auth": {"type": "object", "description": "Authentication details."}}, "required": ["method", "url"]}}}}, "responses": {"200": {"description": "Request successful.", "content": {"application/json": {"schema": {"type": "object", "properties": {"status_code": {"type": "number", "description": "The HTTP status code."}, "headers": {"type": "object", "description": "The response headers."}, "body": {"type": "object", "description": "The response body."}}}}}}}}}}}