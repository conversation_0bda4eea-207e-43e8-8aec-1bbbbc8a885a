{"name": "@cktmcs/post-office", "version": "1.0.0", "description": "Central message routing component for the system", "main": "dist/PostOffice.js", "scripts": {"start": "ts-node src/PostOffice.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@cktmcs/errorhandler": "file:../../errorhandler", "@cktmcs/shared": "file:../../shared", "axios": "^1.8.4", "body-parser": "^1.19.0", "cors": "^2.8.5", "express": "^4.21.1", "express-rate-limit": "^7.5.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "uuid": "^10.0.0", "ws": "^8.18.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.12", "jest": "^29.7.0", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "^5.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "overrides": {"glob": "^10.4.5", "inflight": "^2.0.0"}}