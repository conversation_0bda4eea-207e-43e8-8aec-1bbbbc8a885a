.step-overview-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(255,255,255,0.98);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.step-overview-content {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 32px rgba(0,0,0,0.18);
  padding: 2.5rem 2.5rem 2rem 2.5rem;
  max-width: 600px;
  width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  font-size: 1.1rem;
}

.close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #eee;
  border: none;
  border-radius: 50%;
  width: 2.2rem;
  height: 2.2rem;
  font-size: 1.5rem;
  cursor: pointer;
  transition: background 0.2s;
  z-index: 1;
}
.close-btn:hover {
  background: #f44336;
  color: #fff;
}

.step-overview-content h3 {
  margin-top: 0;
  margin-bottom: 1.2rem;
  font-size: 1.5rem;
}

.step-overview-content pre {
  background: #f5f5f5;
  border-radius: 6px;
  padding: 0.7em 1em;
  font-size: 0.98em;
  overflow-x: auto;
}
