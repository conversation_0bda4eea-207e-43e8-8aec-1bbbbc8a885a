{"name": "@cktmcs/capabilities-manager", "version": "1.0.0", "description": "Capabilities Manager component to handle ActionVerbs and Plugins", "main": "dist/index.js", "type": "commonjs", "scripts": {"start": "ts-node src/index.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@cktmcs/errorhandler": "file:../../errorhandler", "@cktmcs/marketplace": "file:../../marketplace", "@cktmcs/shared": "file:../../shared", "archiver": "^7.0.1", "axios": "^1.8.4", "body-parser": "^1.19.0", "cheerio": "^1.0.0", "cors": "^2.8.5", "dockerode": "^4.0.7", "dotenv": "^16.5.0", "express": "^4.21.1", "extract-zip": "^2.0.1", "json-alexander": "^0.1.13", "simple-git": "^3.27.0", "ws": "^8.18.2"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/cheerio": "^0.22.35", "@types/cors": "^2.8.17", "@types/dockerode": "^3.3.31", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/mocha": "^10.0.9", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "^5.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}}