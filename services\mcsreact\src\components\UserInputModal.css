.user-input-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-input-modal .modal-backdrop {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 1001;
}
.user-input-modal .modal-content {
  background: #fff;
  border-radius: 8px;
  padding: 2rem 2.5rem;
  box-shadow: 0 4px 32px rgba(0,0,0,0.18);
  z-index: 1002;
  min-width: 320px;
  max-width: 90vw;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.user-input-modal .modal-question {
  font-size: 1.1rem;
  margin-bottom: 1rem;
}
.user-input-modal .modal-input {
  margin-bottom: 1.5rem;
}
.user-input-modal .modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}
.user-input-modal .modal-submit {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  cursor: pointer;
}
.user-input-modal .modal-cancel {
  background: #eee;
  color: #333;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  cursor: pointer;
}
.user-input-modal input[type="text"],
.user-input-modal input[type="number"] {
  width: 100%;
  padding: 0.5rem;
  font-size: 1rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.user-input-modal button {
  transition: background 0.2s;
}
.user-input-modal .modal-submit:hover {
  background: #115293;
}
.user-input-modal .modal-cancel:hover {
  background: #ccc;
}
