{
    "compilerOptions": {
      "target": "ES2020",                  // Target modern JavaScript (ES2020)
      "module": "commonjs",                // Use CommonJS module system (for Node.js)

      "strict": true,                      // Enable strict type checking
      "esModuleInterop": true,             // Enables compatibility with ES6 modules
      "skipLibCheck": true,                // Skips type checking of declaration files
      "forceConsistentCasingInFileNames": true,  // Ensure consistent file name casing
      "outDir": "./dist",                  // Output directory for compiled files
      "rootDir": "./src",                      // Root directory of source files
      "baseUrl": ".",
      "paths": {
        "@cktmcs/shared": ["../../shared/dist"],
        "@cktmcs/shared/*": ["../../shared/dist/*"]
      },
      "types": ["express"],
      "resolveJsonModule": true,           // Allow importing JSON modules
      "sourceMap": true,                   // Generate source maps for debugging
      "allowJs": true,                     // Allow JavaScript files to be compiled
      "importHelpers": true                // Import helper functions from tslib
    },
    "include": ["src/**/*"],               // Include all files in the `src` directory
    "exclude": ["node_modules"]            // Exclude `node_modules` directory
  }
