# Use an official Node.js runtime as the base image
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./
COPY shared/package*.json ./shared/
COPY errorhandler/package*.json ./errorhandler/
COPY marketplace/package*.json ./marketplace/
COPY services/engineer/package*.json ./services/engineer/

# Install the application dependencies
RUN npm install

# Copy the application code to the working directory
COPY shared ./shared
COPY errorhandler ./errorhandler
COPY marketplace ./marketplace
COPY services/engineer ./services/engineer

# Build the TypeScript code
RUN cd shared && npm run build
RUN cd errorhandler && npm run build
RUN cd marketplace && npm run build
RUN npm run build --workspace=services/engineer

# Set the working directory to the service
WORKDIR /usr/src/app/services/engineer

# Expose the port the app runs on
EXPOSE 5050

# Define the command to run the application
CMD ["node", "dist/Engineer.js"]