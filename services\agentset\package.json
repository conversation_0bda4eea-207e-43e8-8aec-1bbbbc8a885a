{"name": "@cktmcs/agent-set", "version": "1.0.0", "description": "AgentSet application for managing multiple agents within a set.", "main": "dist/AgentSet.js", "scripts": {"start": "ts-node src/AgentSet.ts", "build": "tsc", "test": "jest", "dev": "ts-node-dev --respawn src/AgentSet.ts"}, "dependencies": {"@cktmcs/shared": "file:../../shared", "@cktmcs/errorhandler": "file:../../errorhandler", "axios": "^1.8.4", "body-parser": "^1.19.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.1", "uuid": "^10.0.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.12", "jest": "^29.7.0", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "overrides": {"glob": "^10.4.5", "inflight": "^2.0.0"}}